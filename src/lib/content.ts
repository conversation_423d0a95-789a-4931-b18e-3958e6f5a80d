import siteConfig from '../../content/site.json';
import architectureIndex from '../../content/architecture/index.json';
import architectureFoundations from '../../content/architecture/architecture-foundations.json';
import devopsIndex from '../../content/devops/index.json';
import platformMaturity from '../../content/devops/platform-maturity.json';
import agileIndex from '../../content/agile/index.json';
import teamPractices from '../../content/agile/team-practices.json';
import qualityIndex from '../../content/quality/index.json';
import testingStrategy from '../../content/quality/testing-strategy.json';

// Static content registry
const contentRegistry = {
  'site.json': siteConfig,
  'architecture/index.json': architectureIndex,
  'architecture/architecture-foundations.json': architectureFoundations,
  'devops/index.json': devopsIndex,
  'devops/platform-maturity.json': platformMaturity,
  'agile/index.json': agileIndex,
  'agile/team-practices.json': teamPractices,
  'quality/index.json': qualityIndex,
  'quality/testing-strategy.json': testingStrategy,
};

export function readJson<T>(filePath: string): T {
  const content = contentRegistry[filePath as keyof typeof contentRegistry];
  if (!content) {
    throw new Error(`Content not found: ${filePath}`);
  }
  return content as T;
}

export function listThemes(): string[] {
  return ['architecture', 'devops', 'agile', 'quality'];
}

export function getThemeIndex(theme: string) {
  return readJson<{
    theme: string;
    title: string;
    descriptionMd?: string;
    models: {modelId: string; title: string}[];
  }>(`${theme}/index.json`);
}

export function getModel(theme: string, modelId: string) {
  return readJson<{
    schemaVersion: string;
    theme: string;
    modelId: string;
    title: string;
    shortDescription?: string;
    overviewMd?: string;
    levels?: {id: string; label: string; color?: string}[];
    dimensions: {
      id: string;
      label: string;
      cells: Record<string, string>;
      assessmentMd?: string;
      nextStepsMd?: string;
    }[];
    howToAssessMd?: string;
    improvementPathsMd?: string;
    references?: {label: string; url: string}[];
    tags?: string[];
    version?: string;
    lastUpdated?: string;
  }>(`${theme}/${modelId}.json`);
}

export function getSiteConfig() {
  return readJson<{
    siteTitle: string;
    tagline: string;
    defaultLevels: {id: string; label: string; color?: string}[];
    footerMd?: string;
    links?: {label: string; url: string}[];
    overviewMd?: string;
  }>('site.json');
}

export function getAllThemes() {
  const themes = listThemes();
  return themes
    .map((theme) => {
      try {
        return getThemeIndex(theme);
      } catch {
        return null;
      }
    })
    .filter((t) => !!t);
}

// Build search index from all content
export function buildSearchIndex() {
  const themes = getAllThemes();
  const index: {
    theme: string;
    modelId: string;
    modelTitle: string;
    dimensionId?: string;
    dimensionLabel?: string;
    text: string;
    url: string;
  }[] = [];

  themes.forEach((theme) => {
    if (!theme) return;

    theme.models.forEach((modelRef) => {
      try {
        const model = getModel(theme.theme, modelRef.modelId);

        // Add model to index
        index.push({
          theme: theme.theme,
          modelId: model.modelId,
          modelTitle: model.title,
          text: `${model.title} ${model.shortDescription || ''} ${model.overviewMd || ''}`,
          url: `/t/${theme.theme}/${model.modelId}`,
        });

        // Add dimensions to index
        model.dimensions.forEach((dimension) => {
          const cellText = Object.values(dimension.cells).join(' ');
          index.push({
            theme: theme.theme,
            modelId: model.modelId,
            modelTitle: model.title,
            dimensionId: dimension.id,
            dimensionLabel: dimension.label,
            text: `${dimension.label} ${cellText} ${dimension.assessmentMd || ''} ${dimension.nextStepsMd || ''}`,
            url: `/t/${theme.theme}/${model.modelId}/d/${dimension.id}`,
          });
        });
      } catch (error) {
        console.warn(`Failed to index model ${modelRef.modelId}:`, error);
      }
    });
  });

  return index;
}
